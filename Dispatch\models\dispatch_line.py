from odoo import models, fields, api, _ # type: ignore
from odoo.exceptions import UserError, ValidationError # type: ignore

class DispatchLine(models.Model):
    _name = 'dtech.dispatch.line'
    _description = 'Ligne de dispatch'
    _order = 'partner_priority desc, product_id, partner_id'
    
    dispatch_id = fields.Many2one('dtech.dispatch.order', string='Dispatch', 
                                 required=True, ondelete='cascade')
    product_id = fields.Many2one('product.product', string='Produit')
    partner_id = fields.Many2one('res.partner', string='Client')
    sale_order_id = fields.Many2one('sale.order', string='Devis')
    
    # Quantités
    quantity = fields.Float('Quantité demandée', required=True, default=0.0)
    dispatched_quantity = fields.Float('Quantité dispatchée', default=0.0)
    available_quantity = fields.Float('Quantité disponible', compute='_compute_available_quantity', store=True)
    suggested_quantity = fields.Float('Quantité suggérée', compute='_compute_suggested_quantity', store=True)
    remaining_quantity = fields.Float('Quantité restante', compute='_compute_remaining_quantity', store=True)
    
    # Informations sur le client
    partner_priority = fields.Selection(related='partner_id.priority', string='Priorité', store=True)
    
    # États pour l'affichage
    dispatch_state = fields.Selection(related='dispatch_id.state', string='État dispatch', store=True)
    
    # Champs calculés pour le tri et filtrage
    is_fully_dispatched = fields.Boolean('Complètement dispatché', compute='_compute_dispatch_status', store=True)
    is_partially_dispatched = fields.Boolean('Partiellement dispatché', compute='_compute_dispatch_status', store=True)
    is_pending = fields.Boolean('En attente', compute='_compute_dispatch_status', store=True)
    
    # Champs additionnels pour les informations produit
    product_uom_id = fields.Many2one(related='product_id.uom_id', string='Unité de mesure', store=True)
    product_default_code = fields.Char(related='product_id.default_code', string='Référence interne', store=True)
    
    # Notes et commentaires
    notes = fields.Text('Notes')
    
    @api.depends('dispatch_id.purchase_order_ids', 'product_id', 'dispatch_id.dispatch_line_ids.dispatched_quantity')
    def _compute_available_quantity(self):
        """Calcule la quantité disponible basée sur les bons de commande"""
        for line in self:
            if not line.dispatch_id or not line.product_id:
                line.available_quantity = 0.0
                continue
                
            try:
                # Récupère la quantité totale commandée pour ce produit
                total_purchased = 0.0
                if line.dispatch_id.purchase_order_ids:
                    purchase_lines = line.dispatch_id.purchase_order_ids.mapped('order_line').filtered(
                        lambda l: l.product_id == line.product_id
                    )
                    total_purchased = sum(purchase_lines.mapped('product_qty'))
                
                # Récupère la quantité déjà dispatchée pour ce produit (autres lignes)
                already_dispatched = 0.0
                if line.dispatch_id.dispatch_line_ids:
                    other_lines = line.dispatch_id.dispatch_line_ids.filtered(
                        lambda l: l.product_id == line.product_id and l.id != line.id
                    )
                    already_dispatched = sum(other_lines.mapped('dispatched_quantity'))
                
                line.available_quantity = max(0, total_purchased - already_dispatched)
            except Exception:
                # En cas d'erreur (par exemple lors de la création), on met une valeur par défaut
                line.available_quantity = 0.0
    
    @api.depends('product_id', 'partner_id', 'quantity')
    def _compute_suggested_quantity(self):
        """Suggère une quantité à dispatcher basée sur les BL existants"""
        for record in self:
            record.suggested_quantity = 0.0
            
            # Recherche des BL pour ce produit et ce client
            pickings = self.env['stock.picking'].search([
                ('partner_id', '=', record.partner_id.id),
                ('state', 'not in', ['done', 'cancel']),
                ('move_ids.product_id', '=', record.product_id.id),
            ])
            
            # Calcule la quantité totale disponible dans les BL
            total_available_quantity = 0.0
            for picking in pickings:
                for move in picking.move_ids:
                    if move.product_id == record.product_id:
                        total_available_quantity += move.product_uom_qty - move.quantity
            
            # La quantité suggérée est le minimum entre la quantité restante et la quantité disponible
            record.suggested_quantity = min(record.remaining_quantity, total_available_quantity)
    
    @api.depends('quantity', 'dispatched_quantity')
    def _compute_remaining_quantity(self):
        """Calcule la quantité restante à dispatcher"""
        for line in self:
            line.remaining_quantity = max(0, line.quantity - line.dispatched_quantity)
    
    @api.depends('quantity', 'dispatched_quantity')
    def _compute_dispatch_status(self):
        """Calcule les statuts de dispatch"""
        for line in self:
            if line.dispatched_quantity <= 0:
                line.is_pending = True
                line.is_partially_dispatched = False
                line.is_fully_dispatched = False
            elif line.dispatched_quantity >= line.quantity:
                line.is_pending = False
                line.is_partially_dispatched = False
                line.is_fully_dispatched = True
            else:
                line.is_pending = False
                line.is_partially_dispatched = True
                line.is_fully_dispatched = False
    
    @api.constrains('dispatched_quantity', 'quantity')
    def _check_dispatched_quantity(self):
        """Vérifie que la quantité dispatchée ne dépasse pas la quantité demandée"""
        for line in self:
            if line.dispatched_quantity < 0:
                raise ValidationError(_('La quantité dispatchée ne peut pas être négative.'))
            if line.dispatched_quantity > line.quantity:
                raise ValidationError(_('La quantité dispatchée ne peut pas dépasser la quantité demandée.'))
    
    @api.constrains('dispatched_quantity')
    def _check_available_quantity(self):
        """Vérifie que la quantité dispatchée ne dépasse pas la quantité disponible"""
        for line in self:
        # Skip validation during creation or if fields are not properly set
            if not line.id or not line.product_id or not line.partner_id:
                continue
            # Skip if available_quantity is not computed yet
            if line.available_quantity == 0:
                continue
                
            if line.dispatched_quantity > line.available_quantity:
                raise ValidationError(_(
                    'La quantité dispatchée (%s) ne peut pas dépasser la quantité disponible (%s) '
                    'pour le produit %s.'
                ) % (line.dispatched_quantity, line.available_quantity, line.product_id.name))
    
    @api.constrains('quantity')
    def _check_quantity_positive(self):
        """Vérifie que la quantité demandée est positive"""
        for line in self:
            # Skip validation during creation if quantity is not set yet or if we're in onchange
            if not line.id or line.quantity == 0:
                continue
            if line.quantity <= 0:
                raise ValidationError(_('La quantité demandée doit être positive.'))
    
    def action_apply_suggestion(self):
        """Applique la quantité suggérée"""
        self.ensure_one()
        if self.suggested_quantity > 0:
            self.dispatched_quantity = self.suggested_quantity
        else:
            raise UserError(_('Aucune quantité suggérée disponible pour cette ligne.'))
    
    def action_dispatch_all(self):
        """Dispatche la quantité totale disponible"""
        self.ensure_one()
        max_quantity = min(self.quantity, self.available_quantity)
        if max_quantity > 0:
            self.dispatched_quantity = max_quantity
        else:
            raise UserError(_('Aucune quantité disponible pour cette ligne.'))
    
    def action_reset_quantity(self):
        """Remet à zéro la quantité dispatchée"""
        self.ensure_one()
        self.dispatched_quantity = 0.0
    
    @api.onchange('dispatched_quantity')
    def _onchange_dispatched_quantity(self):
        """Valide la quantité dispatchée lors de la saisie"""
        if self.dispatched_quantity < 0:
            return {
                'warning': {
                    'title': _('Attention'),
                    'message': _('La quantité dispatchée ne peut pas être négative.')
                }
            }
        if self.dispatched_quantity > self.quantity:
            return {
                'warning': {
                    'title': _('Attention'),
                    'message': _('La quantité dispatchée ne peut pas dépasser la quantité demandée.')
                }
            }
        # Only check available quantity if it's computed and greater than 0
        if hasattr(self, 'available_quantity') and self.available_quantity > 0 and self.dispatched_quantity > self.available_quantity:
            return {
                'warning': {
                    'title': _('Attention'),
                    'message': _('La quantité dispatchée ne peut pas dépasser la quantité disponible.')
                }
            }
    
    def name_get(self):
        """Personnalise l'affichage du nom de la ligne"""
        result = []
        for line in self:
            name = f"{line.product_id.name or ''} - {line.partner_id.name or ''}"
            if line.sale_order_id:
                name += f" ({line.sale_order_id.name})"
            result.append((line.id, name))
        return result
    
 
    
    def write(self, vals):
        """Actions à la modification d'une ligne"""
        result = super().write(vals)
        
        # Si la quantité dispatchée change, recalcule les quantités disponibles
        if 'dispatched_quantity' in vals:
            for line in self:
                self._recompute_available_quantities(line.dispatch_id, line.product_id)
        return result
    
    def unlink(self):
        """Actions à la suppression d'une ligne"""
        # Mémorise les informations nécessaires avant suppression
        products_and_dispatches = [(line.product_id, line.dispatch_id) for line in self]
        result = super().unlink()
        
        # Recalcule les quantités disponibles des autres lignes
        for product, dispatch in products_and_dispatches:
            self._recompute_available_quantities(dispatch, product)
        return result
    
    def _recompute_available_quantities(self, dispatch_order, product):
        """Recalcule les quantités disponibles pour un produit donné dans un dispatch"""
        if not dispatch_order or not product:
            return
            
        try:
            other_lines = dispatch_order.dispatch_line_ids.filtered(
                lambda l: l.product_id == product
            )
            if other_lines:
                other_lines._compute_available_quantity()
        except Exception:
            # Ignore errors during recomputation to avoid blocking operations
            pass

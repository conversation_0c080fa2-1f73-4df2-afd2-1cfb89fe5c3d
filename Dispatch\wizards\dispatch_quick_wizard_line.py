import logging
from odoo import models, fields, api # type: ignore
from odoo.exceptions import UserError , ValidationError  # type: ignore


class DispatchQuickWizardLine(models.TransientModel):
    _name = 'dispatch.quick.wizard.line'
    _description = 'Ligne de l\'assistant de dispatch rapide'

    wizard_id = fields.Many2one('dispatch.quick.wizard', string='Wizard', required=True, ondelete='cascade')
    dispatch_line_id = fields.Many2one('dtech.dispatch.line', string='Ligne de dispatch', required=True)
    
    # Product and partner info
    product_id = fields.Many2one('product.product', string='Produit', required=True)
    partner_id = fields.Many2one('res.partner', string='Client', required=True)
    sale_order_id = fields.Many2one('sale.order', string='Devis')
    
    # Quantities
    quantity = fields.Float('Quantité demandée', required=True)
    dispatched_quantity = fields.Float('Quantité à dispatcher', required=True)
    available_quantity = fields.Float('Quantité disponible', readonly=True)
    suggested_quantity = fields.Float('Quantité suggérée', readonly=True)
    
    # Computed fields for display
    remaining_quantity = fields.Float('Reste après dispatch', compute='_compute_remaining_quantity')
    client_priority = fields.Selection(related='partner_id.priority', string='Priorité client', readonly=True)
    
    # Helper fields for filtering
    wizard_product_filter = fields.Many2one(related='wizard_id.product_id_filter', store=False)
    wizard_partner_filter = fields.Many2one(related='wizard_id.partner_id_filter', store=False)
    
    @api.depends('quantity', 'dispatched_quantity')
    def _compute_remaining_quantity(self):
        for line in self:
            line.remaining_quantity = line.quantity - line.dispatched_quantity

    @api.onchange('dispatched_quantity')
    def _onchange_dispatched_quantity(self):
        """Validate dispatched quantity on change"""
        if self.dispatched_quantity < 0:
            return {
                'warning': {
                    'title': 'Quantité invalide',
                    'message': 'La quantité ne peut pas être négative.'
                }
            }
        
        if self.dispatched_quantity > self.quantity:
            return {
                'warning': {
                    'title': 'Quantité élevée',
                    'message': f'La quantité dispatchée ({self.dispatched_quantity}) '
                              f'est supérieure à la quantité demandée ({self.quantity}).'
                }
            }

    def action_set_suggested(self):
        """Set dispatched quantity to suggested quantity"""
        self.dispatched_quantity = self.suggested_quantity

    def action_set_max(self):
        """Set dispatched quantity to maximum (requested quantity)"""
        self.dispatched_quantity = self.quantity

    def action_set_zero(self):
        """Set dispatched quantity to zero"""
        self.dispatched_quantity = 0
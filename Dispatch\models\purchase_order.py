from odoo import models, fields, api # type: ignore

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    dispatch_ids = fields.One2many(
        'dtech.dispatch.order', 'purchase_order_ids',
        string='Dispatchs liés', compute='_compute_dispatch_ids', store=False)
    picking_ids_dispatch = fields.Many2many(
        'stock.picking', string='BL dispatchés', compute='_compute_picking_ids_dispatch', store=False)

    @api.depends('order_line')
    def _compute_dispatch_ids(self):
        for po in self:
            po.dispatch_ids = self.env['dtech.dispatch.order'].search([('purchase_order_ids', 'in', po.id)])

    @api.depends('dispatch_ids')
    def _compute_picking_ids_dispatch(self):
        for po in self:
            pickings = self.env['stock.picking']
            for dispatch in po.dispatch_ids:
                pickings |= dispatch.dispatch_line_ids.mapped('sale_order_id.picking_ids')
            po.picking_ids_dispatch = pickings

    
    def action_view_dispatchs(self):
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Dispatchs liés',
            'res_model': 'dtech.dispatch.order',
            'view_mode': 'tree,form',
            'domain': [('purchase_order_ids', 'in', [self.id])],
            'target': 'current',
        }

    def action_view_pickings_dispatch(self):
        self.ensure_one()
        pickings = self.picking_ids_dispatch
        return {
            'type': 'ir.actions.act_window',
            'name': 'BL dispatchés',
            'res_model': 'stock.picking',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', pickings.ids)],
            'target': 'current',
        } 
    
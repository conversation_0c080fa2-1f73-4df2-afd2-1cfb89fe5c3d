import logging
from odoo import models, fields, api, _ # type: ignore
from odoo.exceptions import UserError, ValidationError # type: ignore


class DispatchOrder(models.Model):
    _name = 'dtech.dispatch.order'
    _description = 'Dispatch Order' 
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char('Référence', required=True, copy=False, 
                      readonly=True, default=lambda self: _('New'))
    purchase_order_ids = fields.Many2many('purchase.order', string='Bons de Commande',
                                        required=True, tracking=True)
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('in_progress', 'En cours'),
        ('done', 'Terminé'),
        ('cancelled', 'Annulé')
    ], string='État', default='draft', tracking=True)
    
    date = fields.Date('Date', default=fields.Date.context_today, required=True)
    user_id = fields.Many2one('res.users', string='Responsable',
                             default=lambda self: self.env.user)
    
    # Main dispatch lines (all lines)
    dispatch_line_ids = fields.One2many('dtech.dispatch.line', 'dispatch_id',
                                      string='Lignes de dispatch', store=True)
    
    # Computed filtered dispatch lines - KEY CHANGE HERE
    filtered_dispatch_line_ids = fields.One2many('dtech.dispatch.line', 'dispatch_id',
                                                string='Lignes filtrées',
                                                compute='_compute_filtered_dispatch_lines',
                                                readonly=True)
    
    # Filter fields
    filter_product_id = fields.Many2one('product.product', string='Filtrer par produit',
                                       help="Filtrer les lignes par produit")
    filter_partner_id = fields.Many2one('res.partner', string='Filtrer par client',
                                       help="Filtrer les lignes par client")
    filter_priority = fields.Selection([
        ('all', 'Tous'),
        ('0', 'Normal'),
        ('1', 'Urgent'),
        ('2', 'Très urgent')
    ], string='Filtrer par priorité', default='all')
    
    show_only_pending = fields.Boolean('Afficher seulement les lignes en attente',
                                      help="Afficher seulement les lignes avec quantité restante > 0")
    
    # Statistics fields
    total_quantity = fields.Float(compute='_compute_total_quantity',
                                string='Quantité totale', store=True)
    dispatched_quantity = fields.Float(compute='_compute_dispatched_quantity',
                                     string='Quantité dispatchée', store=True)
    remaining_quantity = fields.Float(compute='_compute_remaining_quantity',
                                    string='Quantité restante', store=True)
    
    # Filtered statistics
    filtered_total_quantity = fields.Float(compute='_compute_filtered_stats',
                                         string='Total filtré')
    filtered_dispatched_quantity = fields.Float(compute='_compute_filtered_stats',
                                               string='Dispatché filtré')
    filtered_remaining_quantity = fields.Float(compute='_compute_filtered_stats',
                                             string='Restant filtré')
    filtered_lines_count = fields.Integer(compute='_compute_filtered_stats',
                                         string='Nombre de lignes filtrées')
    
    # Other fields
    arrival_date = fields.Date('Date d\'arrivée prévue', tracking=True)
    notes = fields.Text('Notes')
    priority = fields.Selection([
        ('0', 'Normal'),
        ('1', 'Urgent'),
        ('2', 'Très urgent')
    ], string='Priorité', default='0', tracking=True)
    
    # Computed fields for search
    partner_ids = fields.Many2many('res.partner', string='Clients',
                                  compute='_compute_partner_ids', store=True)
    product_ids = fields.Many2many('product.product', string='Produits',
                                  compute='_compute_product_ids', store=True)

    sale_order_count = fields.Integer(string='Nombre de devis', compute='_compute_sale_order_count')
    picking_count = fields.Integer(string='Nombre de BL', compute='_compute_picking_count')

    # Champ pour la vue matricielle
    matrix_html = fields.Html('Vue Matricielle', compute='_compute_matrix_html', store=False)

    # NEW METHOD: Compute filtered lines
    @api.depends('dispatch_line_ids', 'filter_product_id', 'filter_partner_id', 'filter_priority', 'show_only_pending')
    def _compute_filtered_dispatch_lines(self):
        """Compute the filtered dispatch lines based on current filters"""
        for record in self:
            filtered_lines = record.dispatch_line_ids
            
            # Apply filters
            if record.filter_product_id:
                filtered_lines = filtered_lines.filtered(lambda l: l.product_id == record.filter_product_id)
            
            if record.filter_partner_id:
                filtered_lines = filtered_lines.filtered(lambda l: l.partner_id == record.filter_partner_id)
            
            if record.filter_priority and record.filter_priority != 'all':
                filtered_lines = filtered_lines.filtered(lambda l: l.partner_priority == record.filter_priority)
            
            if record.show_only_pending:
                filtered_lines = filtered_lines.filtered(lambda l: l.remaining_quantity > 0)
            
            record.filtered_dispatch_line_ids = filtered_lines


    def _get_filter_domain(self):
        """Get the current filter domain for filtered_dispatch_line_ids"""
        domain = []
        
        # Product filter
        if self.filter_product_id:
            domain.append(('product_id', '=', self.filter_product_id.id))
        
        # Partner filter
        if self.filter_partner_id:
            domain.append(('partner_id', '=', self.filter_partner_id.id))
        
        # Priority filter
        if self.filter_priority and self.filter_priority != 'all':
            domain.append(('partner_priority', '=', self.filter_priority))
        
        # Pending filter
        if self.show_only_pending:
            domain.append(('remaining_quantity', '>', 0))
        
        return domain

    # SIMPLIFIED ONCHANGE - just for UI refresh if needed
    @api.onchange('filter_product_id', 'filter_partner_id', 'filter_priority', 'show_only_pending')
    def _onchange_filters(self):
        """Update filtered lines when filters change"""
        # The computed field will handle the filtering automatically
        # This onchange is kept for any additional UI logic if needed
        pass

    @api.depends('dispatch_line_ids', 'filter_product_id', 'filter_partner_id', 'filter_priority', 'show_only_pending')
    def _compute_filtered_stats(self):
        """Compute statistics for filtered lines"""
        for record in self:
            # Use the computed filtered lines
            filtered_lines = record.filtered_dispatch_line_ids

            # Calculate stats
            record.filtered_lines_count = len(filtered_lines)
            record.filtered_total_quantity = sum(filtered_lines.mapped('quantity'))
            record.filtered_dispatched_quantity = sum(filtered_lines.mapped('dispatched_quantity'))
            record.filtered_remaining_quantity = sum(filtered_lines.mapped('remaining_quantity'))

    @api.depends('filtered_dispatch_line_ids', 'dispatch_line_ids')
    def _compute_matrix_html(self):
        """Calcule le HTML de la vue matricielle"""
        for record in self:
            try:
                record.matrix_html = record.generate_matrix_html()
            except Exception as e:
                logging.error(f"Erreur lors de la génération de la matrice HTML: {e}")
                record.matrix_html = f'<div class="alert alert-danger">Erreur lors de la génération de la matrice: {str(e)}</div>'

    @api.onchange('purchase_order_ids')
    def _onchange_purchase_order_ids(self):
        """Auto-generate dispatch lines when purchase orders change"""
        logging.info("Purchase orders changed, auto-generating dispatch lines")
        
        # Clear existing lines first
        self.dispatch_line_ids = [(5, 0, 0)]
        
        if not self.purchase_order_ids:
            return
        
        # Get products from purchase orders
        purchase_lines = self.purchase_order_ids.mapped('order_line')
        products = purchase_lines.mapped('product_id')
        
        if not products:
            return
        
        # Search for sale orders with these products
        sale_orders = self.env['sale.order'].search([
            ('state', 'in', ['draft', 'sent']),
            ('order_line.product_id', 'in', products.ids)
        ])
        
        if not sale_orders:
            return
        
        # Create dispatch lines
        lines_to_create = []
        processed_combinations = set()
        
        for sale_order in sale_orders:
            for sale_line in sale_order.order_line:
                if (sale_line.product_id in products and 
                    sale_line.product_uom_qty > 0):
                    
                    # Create unique key
                    key = (sale_line.product_id.id, sale_order.partner_id.id, sale_order.id)
                    
                    if key not in processed_combinations:
                        lines_to_create.append((0, 0, {
                            'product_id': sale_line.product_id.id,
                            'partner_id': sale_order.partner_id.id,
                            'sale_order_id': sale_order.id,
                            'quantity': sale_line.product_uom_qty,
                            'dispatched_quantity': 0.0,
                        }))
                        processed_combinations.add(key)
        
        # Set the lines using the proper format for onchange
        if lines_to_create:
            self.dispatch_line_ids = lines_to_create
            logging.info(f"Auto-generated {len(lines_to_create)} dispatch lines")

    # Keep the manual button method for explicit generation
    def action_generate_lines(self):
        """Manual generation of dispatch lines (button action)"""
        logging.info("Manually generating dispatch lines from selected purchase orders")
        
        if not self.purchase_order_ids:
            raise UserError(_('Veuillez sélectionner au moins un bon de commande.'))
        
        # Clear existing lines
        self.dispatch_line_ids = [(5, 0, 0)]
        
        # Get products from purchase orders
        purchase_lines = self.purchase_order_ids.mapped('order_line')
        products = purchase_lines.mapped('product_id')
        
        if not products:
            raise UserError(_('Aucun produit trouvé dans les bons de commande sélectionnés.'))
        
        # Search for sale orders with these products
        sale_orders = self.env['sale.order'].search([
            ('state', 'in', ['draft', 'sent']),
            ('order_line.product_id', 'in', products.ids)
        ])
        
        if not sale_orders:
            raise UserError(_('Aucun devis trouvé avec ces produits.'))
        
        # Create dispatch lines
        lines_to_create = []
        processed_combinations = set()
        
        for sale_order in sale_orders:
            for sale_line in sale_order.order_line:
                if (sale_line.product_id in products and 
                    sale_line.product_uom_qty > 0):
                    
                    # Create unique key
                    key = (sale_line.product_id.id, sale_order.partner_id.id, sale_order.id)
                    
                    if key not in processed_combinations:
                        lines_to_create.append({
                            'dispatch_id': self.id,
                            'product_id': sale_line.product_id.id,
                            'partner_id': sale_order.partner_id.id,
                            'sale_order_id': sale_order.id,
                            'quantity': sale_line.product_uom_qty,
                            'dispatched_quantity': 0.0,
                        })
                        processed_combinations.add(key)
        
        # Create the lines in database
        if lines_to_create:
            created_lines = self.env['dtech.dispatch.line'].create(lines_to_create)
            # Refresh the dispatch_line_ids field
            self.dispatch_line_ids = created_lines
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': f'{len(lines_to_create)} lignes de dispatch créées.',
                    'type': 'success',
                    'sticky': False,
                }
            }
        else:
            raise UserError(_('Aucune ligne de dispatch à créer.'))

    def _generate_dispatch_lines(self):
        """Internal method to generate dispatch lines (used in create method)"""
        if not self.purchase_order_ids:
            return
        
        # Get products from purchase orders
        purchase_lines = self.purchase_order_ids.mapped('order_line')
        products = purchase_lines.mapped('product_id')
        
        if not products:
            return
        
        # Search for sale orders
        sale_orders = self.env['sale.order'].search([
            ('state', 'in', ['draft', 'sent']),
            ('order_line.product_id', 'in', products.ids)
        ])
        
        # Create lines
        lines_to_create = []
        processed_combinations = set()
        
        for sale_order in sale_orders:
            for sale_line in sale_order.order_line:
                if (sale_line.product_id in products and 
                    sale_line.product_uom_qty > 0):
                    
                    key = (sale_line.product_id.id, sale_order.partner_id.id, sale_order.id)
                    
                    if key not in processed_combinations:
                        lines_to_create.append({
                            'dispatch_id': self.id,
                            'product_id': sale_line.product_id.id,
                            'partner_id': sale_order.partner_id.id,
                            'sale_order_id': sale_order.id,
                            'quantity': sale_line.product_uom_qty,
                            'dispatched_quantity': 0.0,
                        })
                        processed_combinations.add(key)
        
        if lines_to_create:
            self.env['dtech.dispatch.line'].create(lines_to_create)
            logging.info(f"Created {len(lines_to_create)} dispatch lines for order {self.name}")

    @api.depends('dispatch_line_ids.partner_id')
    def _compute_partner_ids(self):
        for record in self:
            record.partner_ids = record.dispatch_line_ids.mapped('partner_id')
    
    @api.depends('dispatch_line_ids.product_id')
    def _compute_product_ids(self):
        for record in self:
            record.product_ids = record.dispatch_line_ids.mapped('product_id')
    
    @api.model_create_multi
    def create(self, vals_list):
        logging.info(f"Creating Dispatch Order with values: {vals_list}")
        for vals in vals_list:
            if vals.get('name', _('New')) == _('New'):
                vals['name'] = self.env['ir.sequence'].next_by_code('dtech.dispatch.order') or _('New')
        
        records = super().create(vals_list)
        
        # Generate lines automatically after creation if purchase orders are present
        for record in records:
            if record.purchase_order_ids:
                record.dispatch_line_ids.unlink()
                record._generate_dispatch_lines()
        
        return records

    def write(self, vals):
        logging.info(f"Updating Dispatch Order {self.name} with values: {vals}")
        if 'name' in vals and vals['name'] == _('New'):
            raise ValidationError(_('Vous ne pouvez pas modifier le nom du dispatch.'))
        
        result = super().write(vals)
        
        # If purchase orders are updated, regenerate lines
        if 'purchase_order_ids' in vals:
            for record in self:
                if record.purchase_order_ids:
                    # Clear existing lines first
                    record.dispatch_line_ids.unlink()
                    # Generate new lines
                    record._generate_dispatch_lines()
        
        return result
    
    @api.depends('dispatch_line_ids.quantity')
    def _compute_total_quantity(self):
        for record in self:
            record.total_quantity = sum(record.dispatch_line_ids.mapped('quantity'))
    
    @api.depends('dispatch_line_ids.dispatched_quantity')
    def _compute_dispatched_quantity(self):
        for record in self:
            record.dispatched_quantity = sum(record.dispatch_line_ids.mapped('dispatched_quantity'))
    
    @api.depends('total_quantity', 'dispatched_quantity')
    def _compute_remaining_quantity(self):
        for record in self:
            record.remaining_quantity = record.total_quantity - record.dispatched_quantity
    
    def action_confirm(self):
        if not self.dispatch_line_ids:
            raise UserError(_('Vous ne pouvez pas confirmer un dispatch sans lignes.'))
        self.write({'state': 'in_progress'})
    
    def action_done(self):
        """Complete the dispatch and update stock picking quantities"""
        for line in self.dispatch_line_ids:
            if line.dispatched_quantity > 0:
                sale_order = line.sale_order_id

                # Confirm sale order if it's in draft or sent state
                if sale_order.state in ['draft', 'sent']:
                    sale_order.action_confirm()

                # Loop through the sale order's delivery orders (stock pickings)
                for picking in sale_order.picking_ids.filtered(lambda p: p.state not in ('done', 'cancel')):
                    # Find moves for the specific product in this picking
                    product_moves = picking.move_ids.filtered(lambda m: m.product_id == line.product_id)
                    
                    for move in product_moves:
                        if move.state in ('confirmed', 'assigned', 'waiting', 'partially_available'):
                            # Update the quantity done with the dispatched quantity
                            move.quantity = min(line.dispatched_quantity, move.product_uom_qty)
                            
                            # If there are move lines (detailed stock moves), update them too
                            if move.move_line_ids:
                                remaining_qty = line.dispatched_quantity
                                for move_line in move.move_line_ids:
                                    if remaining_qty > 0:
                                        qty_to_assign = min(remaining_qty, move_line.quantity_product_uom or move_line.product_uom_qty)
                                        move_line.quantity = qty_to_assign
                                        remaining_qty -= qty_to_assign
                            
                            # Set the move as done if quantity_done equals product_uom_qty
                            if move.quantity >= move.product_uom_qty:
                                move._action_done()

        # Change the dispatch order state to done
        self.write({'state': 'done'})

        self.write({'state': 'done'})
    
    def action_cancel(self):
        self.write({'state': 'cancelled'})
    
    def action_draft(self):
        self.write({'state': 'draft'})

    def action_view_sale_orders(self):
        self.ensure_one()
        sale_orders = self.dispatch_line_ids.mapped('sale_order_id')
        return {
            'type': 'ir.actions.act_window',
            'name': 'Devis liés',
            'res_model': 'sale.order',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', sale_orders.ids)],
            'target': 'current',
        }

    def action_view_pickings(self):
        self.ensure_one()
        pickings = self.dispatch_line_ids.mapped('sale_order_id.picking_ids')
        return {
            'type': 'ir.actions.act_window',
            'name': 'BL liés',
            'res_model': 'stock.picking',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', pickings.ids)],
            'target': 'current',
        }

    def _compute_sale_order_count(self):
        for record in self:
            record.sale_order_count = len(record.dispatch_line_ids.mapped('sale_order_id'))

    def _compute_picking_count(self):
        for record in self:
            record.picking_count = len(record.dispatch_line_ids.mapped('sale_order_id.picking_ids'))

    def action_clear_filters(self):
        """Clear all filters"""
        self.write({
            'filter_product_id': False,
            'filter_partner_id': False,
            'filter_priority': 'all',
            'show_only_pending': False,
        })
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Filtres effacés. Vue matricielle mise à jour.',
                'type': 'success',
                'sticky': False,
            }
        }

    def action_apply_all_suggestions(self):
        """Apply suggested quantities to all filtered lines"""
        filtered_lines = self.filtered_dispatch_line_ids
        
        for line in filtered_lines:
            if hasattr(line, 'suggested_quantity'):
                line.dispatched_quantity = line.suggested_quantity

    def action_reset_filtered_quantities(self):
        """Reset dispatched quantities for filtered lines to 0"""
        filtered_lines = self.filtered_dispatch_line_ids

        count = 0
        for line in filtered_lines:
            if line.dispatched_quantity > 0:
                line.dispatched_quantity = 0
                count += 1

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': f'{count} quantités remises à zéro. Vue matricielle mise à jour.',
                'type': 'success',
                'sticky': False,
            }
        }

    def _get_filtered_lines(self):
        """Get currently filtered lines - now uses the computed field"""
        return self.filtered_dispatch_line_ids

    def action_filter_urgent_clients(self):
        """Filtre pour afficher seulement les clients urgents"""
        self.write({
            'filter_priority': '1',  # Urgent
            'show_only_pending': True,
        })
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Filtrage appliqué: clients urgents avec quantités en attente.',
                'type': 'info',
                'sticky': False,
            }
        }

    def action_filter_very_urgent_clients(self):
        """Filtre pour afficher seulement les clients très urgents"""
        self.write({
            'filter_priority': '2',  # Très urgent
            'show_only_pending': True,
        })
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Filtrage appliqué: clients très urgents avec quantités en attente.',
                'type': 'warning',
                'sticky': False,
            }
        }

    def action_open_matrix_view(self):
        """Ouvre la vue matricielle pour ce dispatch"""
        self.ensure_one()

        return {
            'type': 'ir.actions.act_window',
            'name': f'Vue Matricielle - {self.name}',
            'res_model': 'dtech.dispatch.matrix.transient',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'active_id': self.id,
                'default_dispatch_id': self.id,
            }
        }

    def get_matrix_data(self):
        """Retourne les données formatées pour la vue matricielle"""
        self.ensure_one()

        # Grouper les lignes par produit
        products_data = {}
        all_partners = set()

        for line in self.dispatch_line_ids:
            product_id = line.product_id.id
            partner_id = line.partner_id.id

            all_partners.add((partner_id, line.partner_id.name, line.partner_priority))

            if product_id not in products_data:
                products_data[product_id] = {
                    'product': line.product_id,
                    'total_available': 0,
                    'total_remaining': 0,
                    'clients': {}
                }

            # Calculer les totaux par produit
            if line.available_quantity > products_data[product_id]['total_available']:
                products_data[product_id]['total_available'] = line.available_quantity

            products_data[product_id]['total_remaining'] += line.remaining_quantity

            # Ajouter les données client
            products_data[product_id]['clients'][partner_id] = {
                'line_id': line.id,
                'quantity_requested': line.quantity,
                'quantity_dispatched': line.dispatched_quantity,
                'remaining': line.remaining_quantity,
                'priority': line.partner_priority,
                'sale_order': line.sale_order_id.name if line.sale_order_id else ''
            }

        # Trier les partenaires par priorité puis par nom
        sorted_partners = sorted(all_partners, key=lambda x: (x[2] or '0', x[1]))

        return {
            'products_data': products_data,
            'partners': sorted_partners,
            'dispatch_name': self.name
        }

    def generate_matrix_html(self):
        """Génère le HTML pour la vue matricielle"""
        self.ensure_one()

        matrix_data = self.get_matrix_data()
        products_data = matrix_data['products_data']
        partners = matrix_data['partners']

        # Appliquer les filtres si nécessaire
        filtered_lines = self.filtered_dispatch_line_ids
        if filtered_lines:
            # Filtrer les produits selon les lignes filtrées
            filtered_product_ids = set(filtered_lines.mapped('product_id.id'))
            products_data = {k: v for k, v in products_data.items() if k in filtered_product_ids}

            # Filtrer les partenaires selon les lignes filtrées
            filtered_partner_ids = set(filtered_lines.mapped('partner_id.id'))
            partners = [p for p in partners if p[0] in filtered_partner_ids]

        html = self._build_matrix_html(products_data, partners)
        return html

    def _build_matrix_html(self, products_data, partners):
        """Construit le HTML de la matrice"""

        # En-tête HTML
        html = '''
        <div class="dispatch-matrix-container" style="overflow-x: auto; max-width: 100%;">
            <table class="table table-bordered table-sm" style="min-width: 1000px; font-size: 12px;">
                <thead class="table-dark">
                    <tr>
                        <th rowspan="2" style="min-width: 200px; position: sticky; left: 0; background: #343a40; z-index: 10;">Produit</th>
                        <th rowspan="2" style="min-width: 100px; text-align: center;">Qté Disponible</th>
                        <th rowspan="2" style="min-width: 100px; text-align: center;">Qté Restante</th>
        '''

        # En-têtes des clients
        for partner_id, partner_name, priority in partners:
            priority_class = 'bg-danger' if priority == '2' else 'bg-warning' if priority == '1' else 'bg-secondary'
            html += f'<th colspan="2" class="{priority_class}" style="text-align: center; min-width: 160px;">{partner_name}</th>'

        html += '''
                    </tr>
                    <tr>
        '''

        # Sous-en-têtes pour chaque client
        for _ in partners:
            html += '''
                        <th style="background: #6c757d; color: white; text-align: center; min-width: 80px;">Demandée</th>
                        <th style="background: #6c757d; color: white; text-align: center; min-width: 80px;">Dispatchée</th>
            '''

        html += '''
                    </tr>
                </thead>
                <tbody>
        '''

        # Lignes de données
        for _, product_data in products_data.items():
            product = product_data['product']
            html += f'''
                <tr>
                    <td style="position: sticky; left: 0; background: #f8f9fa; font-weight: 500; border-right: 2px solid #dee2e6;">
                        <strong>{product.name}</strong>
                        {f'<br><small class="text-muted">[{product.default_code}]</small>' if product.default_code else ''}
                    </td>
                    <td style="text-align: center; background: #e9ecef;">
                        <span class="badge badge-info">{product_data['total_available']}</span>
                    </td>
                    <td style="text-align: center; background: #e9ecef;">
                        <span class="badge {'badge-warning' if product_data['total_remaining'] > 0 else 'badge-success'}">{product_data['total_remaining']}</span>
                    </td>
            '''

            # Colonnes clients
            for partner_id, partner_name, priority in partners:
                client_data = product_data['clients'].get(partner_id, {})
                requested = client_data.get('quantity_requested', 0)
                dispatched = client_data.get('quantity_dispatched', 0)

                # Cellule quantité demandée
                html += f'''
                    <td style="text-align: center; {'background: #fff3cd;' if requested > 0 else ''}">
                        {f'<span class="text-primary font-weight-bold">{requested}</span>' if requested > 0 else '-'}
                    </td>
                '''

                # Cellule quantité dispatchée (ÉDITABLE)
                if requested > 0:
                    line_id = client_data.get('line_id')
                    percentage = (dispatched / requested * 100) if requested > 0 else 0
                    bg_color = '#d4edda' if percentage >= 100 else '#fff3cd' if percentage > 0 else '#f8d7da'

                    # TOUJOURS afficher le champ éditable si on a une quantité demandée
                    html += f'''
                        <td style="text-align: center; background: {bg_color}; padding: 4px;">
                            <div class="input-group input-group-sm" style="width: 80px; margin: 0 auto;">
                                <input type="number"
                                       class="form-control text-center dispatch-qty-input"
                                       value="{dispatched}"
                                       min="0"
                                       max="{requested}"
                                       step="0.01"
                                       data-line-id="{line_id or 0}"
                                       data-max="{requested}"
                                       style="font-size: 11px; padding: 2px 4px; height: 24px;"
                                       onchange="updateQuantity(this)"
                                       title="Line ID: {line_id} | Max: {requested}"
                                       {'disabled' if not line_id else ''}>
                            </div>
                            {f'<small style="font-size: 9px; color: #666;">({percentage:.0f}%)</small>' if percentage > 0 else ''}
                            <small style="font-size: 8px; color: #999;">ID: {line_id or 'N/A'}</small>
                        </td>
                    '''
                else:
                    html += '<td style="text-align: center; color: #999;">-</td>'

            html += '</tr>'

        # Ligne de totaux
        html += '''
                    <tr class="table-secondary font-weight-bold">
                        <td style="position: sticky; left: 0; background: #e9ecef;">TOTAUX</td>
        '''

        # Calculer les totaux
        total_available = sum(data['total_available'] for data in products_data.values())
        total_remaining = sum(data['total_remaining'] for data in products_data.values())

        html += f'''
                        <td style="text-align: center; background: #e9ecef;">{total_available}</td>
                        <td style="text-align: center; background: #e9ecef;">{total_remaining}</td>
        '''

        # Totaux par client
        for partner_id, partner_name, priority in partners:
            total_requested = sum(
                data['clients'].get(partner_id, {}).get('quantity_requested', 0)
                for data in products_data.values()
            )
            total_dispatched = sum(
                data['clients'].get(partner_id, {}).get('quantity_dispatched', 0)
                for data in products_data.values()
            )

            html += f'''
                        <td style="text-align: center; background: #e9ecef;">{total_requested}</td>
                        <td style="text-align: center; background: #e9ecef;">{total_dispatched}</td>
            '''

        html += '''
                    </tr>
                </tbody>
            </table>
        </div>

        <style>
            .dispatch-matrix-container {
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                margin: 10px 0;
            }
            .dispatch-matrix-container table {
                margin-bottom: 0;
            }
            .badge-info { background-color: #17a2b8; }
            .badge-warning { background-color: #ffc107; color: #212529; }
            .badge-success { background-color: #28a745; }

            .dispatch-qty-input {
                border: 1px solid #ced4da !important;
                border-radius: 3px !important;
                transition: all 0.3s ease !important;
                font-weight: 500 !important;
            }
            .dispatch-qty-input:focus {
                border-color: #007bff !important;
                box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25) !important;
                outline: none !important;
            }
            .dispatch-qty-input:hover {
                border-color: #80bdff !important;
            }

            /* Animation pour les changements d'état */
            .dispatch-qty-input {
                animation: none;
            }
            .dispatch-qty-input[style*="border-color: #ffc107"] {
                animation: pulse-warning 0.5s ease-in-out;
            }
            .dispatch-qty-input[style*="border-color: #28a745"] {
                animation: pulse-success 0.5s ease-in-out;
            }
            .dispatch-qty-input[style*="border-color: #dc3545"] {
                animation: pulse-error 0.5s ease-in-out;
            }

            @keyframes pulse-warning {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            @keyframes pulse-success {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            @keyframes pulse-error {
                0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
                70% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
                100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
            }
        </style>

        <script>
            // JavaScript pour l'édition dans la vue matricielle
            let updateTimeout;

            function updateQuantity(input) {
                const lineId = parseInt(input.dataset.lineId);
                const newValue = parseFloat(input.value) || 0;
                const maxValue = parseFloat(input.dataset.max) || 0;

                // Vérifier si on a un line_id valide
                if (!lineId || lineId === 0) {
                    showMessage('Impossible de sauvegarder: ligne non identifiée', 'danger');
                    input.disabled = true;
                    return;
                }

                // Validation simple
                if (newValue < 0) {
                    input.value = 0;
                    showMessage('La quantité ne peut pas être négative', 'warning');
                    return;
                }

                if (newValue > maxValue) {
                    input.value = maxValue;
                    showMessage(`Quantité limitée à ${maxValue}`, 'warning');
                    return;
                }

                // Marquer comme modifié
                input.style.borderColor = '#ffc107';
                input.style.backgroundColor = '#fff3cd';

                // Programmer la sauvegarde
                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(() => {
                    saveQuantity(lineId, newValue, input);
                }, 1000);
            }

            async function saveQuantity(lineId, quantity, inputElement) {
                try {
                    // Marquer comme en cours de sauvegarde
                    inputElement.style.borderColor = '#17a2b8';
                    inputElement.style.backgroundColor = '#d1ecf1';
                    inputElement.disabled = true;

                    // Appel AJAX simple
                    const response = await fetch('/web/dataset/call_kw', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            jsonrpc: '2.0',
                            method: 'call',
                            params: {
                                model: 'dtech.dispatch.line',
                                method: 'write',
                                args: [[lineId], {dispatched_quantity: quantity}],
                                kwargs: {}
                            }
                        })
                    });

                    const result = await response.json();

                    if (result.error) {
                        throw new Error(result.error.data.message || 'Erreur de sauvegarde');
                    }

                    // Succès
                    inputElement.style.borderColor = '#28a745';
                    inputElement.style.backgroundColor = '#d4edda';
                    showMessage('Quantité sauvegardée', 'success');

                    // Remettre l'apparence normale après 2 secondes
                    setTimeout(() => {
                        inputElement.style.borderColor = '';
                        inputElement.style.backgroundColor = '';
                        inputElement.disabled = false;
                    }, 2000);

                } catch (error) {
                    console.error('Erreur:', error);
                    inputElement.style.borderColor = '#dc3545';
                    inputElement.style.backgroundColor = '#f8d7da';
                    inputElement.disabled = false;
                    showMessage('Erreur: ' + error.message, 'danger');

                    // Remettre l'apparence normale après 3 secondes
                    setTimeout(() => {
                        inputElement.style.borderColor = '';
                        inputElement.style.backgroundColor = '';
                    }, 3000);
                }
            }

            function showMessage(message, type) {
                // Créer une notification simple
                const alert = document.createElement('div');
                alert.className = `alert alert-${type} alert-dismissible fade show`;
                alert.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                alert.innerHTML = `
                    <strong>${type === 'success' ? '✓' : type === 'warning' ? '⚠' : '✗'}</strong> ${message}
                    <button type="button" class="close" onclick="this.parentElement.remove()">
                        <span>&times;</span>
                    </button>
                `;

                document.body.appendChild(alert);

                // Auto-suppression
                setTimeout(() => {
                    if (alert.parentElement) {
                        alert.remove();
                    }
                }, type === 'success' ? 3000 : 5000);
            }

            // Fonction pour recharger la vue si nécessaire
            function refreshMatrixIfNeeded() {
                // Auto-refresh après 5 modifications pour éviter les problèmes de cache
                const modifiedInputs = document.querySelectorAll('.dispatch-qty-input[data-modified="true"]');
                if (modifiedInputs.length >= 5) {
                    showMessage('Actualisation automatique de la vue...', 'info');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                }
            }

            console.log('Vue matricielle éditable chargée');
        </script>
        '''

        return html

    def action_export_matrix_excel(self):
        """Exporte la vue matricielle vers Excel"""
        self.ensure_one()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Fonctionnalité d\'export Excel à venir dans une prochaine version.',
                'type': 'info',
                'sticky': False,
            }
        }





    @api.model
    def update_matrix_quantities(self, line_updates):
        """
        Met à jour les quantités dispatchées depuis la vue matricielle
        line_updates: [{'line_id': int, 'dispatched_quantity': float}, ...]
        """
        try:
            updated_count = 0
            for update in line_updates:
                line_id = update.get('line_id')
                new_quantity = update.get('dispatched_quantity', 0)

                if line_id:
                    line = self.env['dtech.dispatch.line'].browse(line_id)
                    if line.exists():
                        # Validation
                        if new_quantity < 0:
                            raise ValidationError(f'La quantité ne peut pas être négative pour la ligne {line_id}')
                        if new_quantity > line.quantity:
                            raise ValidationError(f'La quantité dispatchée ({new_quantity}) ne peut pas dépasser la quantité demandée ({line.quantity}) pour la ligne {line_id}')

                        # Mise à jour
                        line.write({'dispatched_quantity': new_quantity})
                        updated_count += 1

            return {
                'success': True,
                'updated_count': updated_count,
                'message': f'{updated_count} ligne(s) mise(s) à jour avec succès'
            }

        except Exception as e:
            logging.error(f"Erreur lors de la mise à jour des quantités matricielles: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f'Erreur lors de la mise à jour: {str(e)}'
            }
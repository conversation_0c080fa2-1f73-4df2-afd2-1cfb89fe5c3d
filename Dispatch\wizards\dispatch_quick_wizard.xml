<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Vue form pour le wizard de dispatch rapide -->
    <record id="view_dispatch_quick_wizard_form" model="ir.ui.view">
        <field name="name">dispatch.quick.wizard.form</field>
        <field name="model">dispatch.quick.wizard</field>
        <field name="arch" type="xml">
            <form string="Dispatch rapide">
                <group>
                    <field name="dispatch_id" invisible="1"/>
                    <field name="available_product_ids" invisible="1"/>
                    <field name="available_partner_ids" invisible="1"/>
                    <field name="product_id_filter"/>
                    <field name="partner_id_filter"/>
                </group>
                
                <!-- Use wizard_line_ids with dynamic domain for filtering -->
                <field name="wizard_line_ids" 
                       context="{'tree_view_ref': 'your_module.view_dispatch_quick_wizard_line_tree'}"
                       domain="[('id', 'in', wizard_line_ids), 
                               '|', ('wizard_product_filter', '=', False), ('product_id', '=', product_id_filter),
                               '|', ('wizard_partner_filter', '=', False), ('partner_id', '=', partner_id_filter)]">
                    <tree editable="inline" create="false" delete="false">
                        <field name="wizard_product_filter" invisible="1"/>
                        <field name="wizard_partner_filter" invisible="1"/>
                        <field name="product_id" readonly="1" string="Produit"/>
                        <field name="partner_id" readonly="1" string="Client"/>
                        <field name="sale_order_id" readonly="1" string="Devis"/>
                        <field name="quantity" readonly="1" string="Quantité demandée"/>
                        <field name="dispatched_quantity" string="Quantité dispatchée" widget="float"/>
                        <field name="suggested_quantity" readonly="1" string="Quantité suggérée"/>
                        <field name="available_quantity" readonly="1" string="Disponible"/>
                        <field name="remaining_quantity" readonly="1" string="Reste"/>
                        <field name="client_priority" readonly="1" widget="selection" string="Priorité"/>
                        <!-- Quick action buttons -->
                        <button name="action_set_suggested" type="object" string="Suggéré" 
                                class="btn-link" title="Utiliser la quantité suggérée"/>
                        <button name="action_set_max" type="object" string="Max" 
                                class="btn-link" title="Utiliser la quantité maximale"/>
                        <button name="action_set_zero" type="object" string="0" 
                                class="btn-link" title="Remettre à zéro"/>
                    </tree>
                </field>
                
                <!-- Action buttons -->
                <group>
                    <button name="action_reset_quantities" string="Tout remettre à zéro" 
                            type="object" class="btn-secondary"/>
                    <button name="action_apply_suggestions" string="Appliquer suggestions" 
                            type="object" class="btn-secondary"/>
                </group>
                
                <footer>
                    <button name="action_apply" string="Valider le dispatch" 
                            type="object" class="btn-primary"/>
                    <button string="Fermer" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Alternative simpler approach: Use attrs to show/hide lines -->
    <record id="view_dispatch_quick_wizard_form_simple" model="ir.ui.view">
        <field name="name">dispatch.quick.wizard.form.simple</field>
        <field name="model">dispatch.quick.wizard</field>
        <field name="arch" type="xml">
            <form string="Dispatch rapide">
                <group>
                    <field name="dispatch_id" invisible="1"/>
                    <field name="available_product_ids" invisible="1"/>
                    <field name="available_partner_ids" invisible="1"/>
                    <field name="product_id_filter"/>
                    <field name="partner_id_filter"/>
                </group>
                
                <!-- Use all wizard lines, filter with attrs -->
                <field name="wizard_line_ids">
                    <tree editable="inline" create="false" delete="false">
                        <field name="product_id" readonly="1" string="Produit" 
                               invisible="context.get('product_filter') and product_id != context.get('product_filter')"/>
                        <field name="partner_id" readonly="1" string="Client"
                               invisible="context.get('partner_filter') and partner_id != context.get('partner_filter')"/>
                        <field name="sale_order_id" readonly="1" string="Devis"/>
                        <field name="quantity" readonly="1" string="Qté demandée"/>
                        <field name="dispatched_quantity" string="Qté dispatchée" widget="float"/>
                        <field name="suggested_quantity" readonly="1" string="Suggérée"/>
                        <field name="available_quantity" readonly="1" string="Disponible"/>
                        <field name="remaining_quantity" readonly="1" string="Reste"/>
                        <field name="client_priority" readonly="1" widget="selection" string="Priorité"/>
                        
                        <!-- Quick action buttons in tree -->
                        <button name="action_set_suggested" type="object" string="S" 
                                class="btn-link oe_link" title="Suggéré" icon="fa-magic"/>
                        <button name="action_set_max" type="object" string="M" 
                                class="btn-link oe_link" title="Maximum" icon="fa-arrow-up"/>
                        <button name="action_set_zero" type="object" string="0" 
                                class="btn-link oe_link" title="Zéro" icon="fa-times"/>
                    </tree>
                </field>
                
                <!-- Bulk action buttons -->
                <div class="oe_button_box">
                    <button name="action_reset_quantities" string="Tout à zéro" 
                            type="object" class="btn-secondary"/>
                    <button name="action_apply_suggestions" string="Appliquer suggestions" 
                            type="object" class="btn-secondary"/>
                </div>
                
                <footer>
                    <button name="action_apply" string="Valider le dispatch" 
                            type="object" class="btn-primary"/>
                    <button string="Fermer" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
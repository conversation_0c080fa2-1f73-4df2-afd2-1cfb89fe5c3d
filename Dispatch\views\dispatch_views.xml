<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_dispatch_order_form" model="ir.ui.view">
        <field name="name">dtech.dispatch.order.form</field>
        <field name="model">dtech.dispatch.order</field>
        <field name="arch" type="xml">
            <form string="Dispatch Order">
                <header>
                    <button name="action_confirm" type="object" string="Confirm" 
                            class="oe_highlight" 
                            invisible="state != 'draft'"/>
                    <button name="action_done" type="object" string="Complete" 
                            class="oe_highlight" 
                            invisible="state != 'in_progress'"/>
                    <button name="action_cancel" type="object" string="Cancel" 
                            invisible="state in ['done', 'cancelled']"/>
                    <button name="action_draft" type="object" string="Reset to Draft" 
                            invisible="state != 'cancelled'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,done"/>
                </header>
    
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button type="object" name="action_view_sale_orders" 
                                class="oe_stat_button" icon="fa-shopping-cart">
                            <field name="sale_order_count" widget="statinfo" string="Sales Orders"/>
                        </button>
                        <button type="object" name="action_view_pickings" 
                                class="oe_stat_button" icon="fa-truck">
                            <field name="picking_count" widget="statinfo" string="Deliveries"/>
                        </button>
                    </div>
    
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
    
                    <group>
                        <group name="main_info" string="Order Information">
                            <field name="date"/>
                            <field name="user_id"/>
                            <field name="priority" widget="priority"/>
                            <field name="arrival_date"/>
                            <field name="product_ids" invisible="True" />
                            <field name="partner_ids" invisible="True"/>
                        </group>
                        <group name="purchase_info" string="Related Purchase Orders">
                            <field name="purchase_order_ids" widget="many2many_tags" 
                                   options="{'color_field': 'state'}"/>
                        </group>
                    </group>
    
                    <!-- Enhanced Filter Section at the top -->
                    <group name="filters" string="Filtres" col="4" class="o_label_nowrap">
                        <field name="filter_product_id" 
                               domain="[('id', 'in', product_ids)]"
                               options="{'no_create': True, 'no_open': True}"
                               placeholder="Filtrer par produit..."/>
                        <field name="filter_partner_id" 
                               domain="[('id', 'in', partner_ids)]"
                               options="{'no_create': True, 'no_open': True}"
                               placeholder="Filtrer par client..."/>
                        <field name="filter_priority"/>
                        <field name="show_only_pending"/>
                    </group>
    
                    <!-- Action buttons in a cleaner layout -->
                    <div class="oe_button_box mb16">
                        <button name="action_clear_filters" type="object"
                                string="Vider les filtres" class="btn btn-secondary mr8"
                                icon="fa-eraser"/>
                        <button name="action_filter_very_urgent_clients" type="object"
                                string="Très Urgent" class="btn btn-danger mr8"
                                icon="fa-exclamation-triangle"/>
                        <button name="action_filter_urgent_clients" type="object"
                                string="Urgent" class="btn btn-warning mr8"
                                icon="fa-exclamation"/>
                        <button name="action_apply_all_suggestions" type="object"
                                string="Appliquer suggestions" class="btn btn-primary mr8"
                                icon="fa-magic"/>
                        <button name="action_reset_filtered_quantities" type="object"
                                string="Remettre à zéro" class="btn btn-outline-warning"
                                icon="fa-refresh"/>
                    </div>
    
                    <!-- Enhanced Stats for filtered results -->
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-3 o_setting_box">
                            <div class="o_setting_left_pane">
                                <i class="fa fa-list fa-lg text-info"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="filtered_lines_count" string="Lignes filtrées"/>
                                <div class="text-muted">
                                    <field name="filtered_lines_count" readonly="1"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-3 o_setting_box">
                            <div class="o_setting_left_pane">
                                <i class="fa fa-cubes fa-lg text-primary"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="filtered_total_quantity" string="Quantité totale"/>
                                <div class="text-muted">
                                    <field name="filtered_total_quantity" readonly="1"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-3 o_setting_box">
                            <div class="o_setting_left_pane">
                                <i class="fa fa-check fa-lg text-success"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="filtered_dispatched_quantity" string="Expédiée"/>
                                <div class="text-muted">
                                    <field name="filtered_dispatched_quantity" readonly="1"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-3 o_setting_box">
                            <div class="o_setting_left_pane">
                                <i class="fa fa-clock-o fa-lg text-warning"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="filtered_remaining_quantity" string="Restante"/>
                                <div class="text-muted">
                                    <field name="filtered_remaining_quantity" readonly="1" 
                                           decoration-danger="filtered_remaining_quantity &gt; 0"/>
                                </div>
                            </div>
                        </div>
                    </div>
    
                    <!-- Dispatch Lines with enhanced features -->
                    <notebook>
                        <page string="Lignes de dispatch" name="dispatch_lines" icon="fa-list">
                            <!-- Use filtered_dispatch_line_ids instead of dispatch_line_ids -->
                            <field name="filtered_dispatch_line_ids" invisible="1"/>
                            <field name="dispatch_line_ids" 
                                   widget="one2many_list"
                                   mode="tree,form"
                                   domain="[('id', 'in', filtered_dispatch_line_ids)]"
                                   nolabel="1">
                                <tree editable="bottom" 
                                      decoration-success="dispatched_quantity &gt;= quantity"
                                      decoration-info="dispatched_quantity &gt; 0 and dispatched_quantity &lt; quantity"
                                      decoration-muted="dispatched_quantity == 0 and quantity &gt; 0"
                                      decoration-warning="remaining_quantity &gt; 0">
                                    <field name="product_id" readonly="1"/>
                                    <field name="partner_id" readonly="1"/>
                                    <field name="sale_order_id" readonly="1"/>
                                    <field name="quantity" readonly="1" sum="Total Demandé"/>
                                    <field name="available_quantity" readonly="1" sum="Total Disponible"/>
                                    <field name="suggested_quantity" readonly="1" sum="Total Suggéré"/>
                                    <field name="dispatched_quantity" sum="Total Expédié"/>
                                    <field name="remaining_quantity" readonly="1" sum="Total Restant"
                                           decoration-danger="remaining_quantity &gt; 0"/>
                                    <field name="partner_priority" readonly="1" widget="priority"/>
                                    <button name="action_apply_suggestion" type="object" 
                                            icon="fa-magic" title="Appliquer suggestion"
                                            class="btn-link text-primary"/>
                                    <button name="action_dispatch_all" type="object" 
                                            icon="fa-arrow-up" title="Quantité maximale"
                                            class="btn-link text-success"/>
                                    <button name="action_reset_quantity" type="object" 
                                            icon="fa-times" title="Remettre à zéro"
                                            class="btn-link text-muted"/>
                                </tree>
                                <form>
                                    <!-- Enhanced form view for editing individual lines -->
                                    <group>
                                        <group>
                                            <field name="product_id"/>
                                            <field name="partner_id"/>
                                            <field name="sale_order_id"/>
                                            <field name="partner_priority" widget="priority"/>
                                        </group>
                                        <group>
                                            <field name="quantity"/>
                                            <field name="available_quantity"/>
                                            <field name="suggested_quantity"/>
                                            <field name="dispatched_quantity"/>
                                            <field name="remaining_quantity"/>
                                        </group>
                                    </group>
                                </form>
                            </field>
                        </page>
                        
                        <page string="Vue Matricielle" name="matrix_view" icon="fa-table">
                            <div class="alert alert-info mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Vue Matricielle Éditable:</strong> Les produits sont affichés en lignes et les clients en colonnes.
                                        <br/><small class="text-muted">
                                            ✏️ <strong>Modifiez directement</strong> les quantités dispatchées dans le tableau.
                                            💾 Sauvegarde automatique après 1 seconde. 🔄 Actualisation automatique si nécessaire.
                                        </small>
                                    </div>
                                    <div>
                                        <button name="action_export_matrix_excel" type="object"
                                                string="Exporter Excel" class="btn btn-success btn-sm"
                                                icon="fa-file-excel-o"/>
                                    </div>
                                </div>
                            </div>

                            <!-- Légende des couleurs -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <strong>Légende:</strong>
                                        <span class="badge badge-success ml-2">Vert = 100% dispatché</span>
                                        <span class="badge badge-warning ml-2">Jaune = Partiellement dispatché</span>
                                        <span class="badge badge-danger ml-2">Rouge = Non dispatché</span>
                                        <span class="badge badge-info ml-2">Bleu = Modification en cours</span>
                                    </small>
                                </div>
                            </div>

                            <field name="matrix_html" nolabel="1" readonly="1"/>
                        </page>

                        <page string="Notes" name="info" icon="fa-info-circle">
                            <group>
                                <field name="notes" placeholder="Ajouter des notes et commentaires..." nolabel="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    
    <!-- Enhanced List View -->
    <record id="view_dispatch_order_tree" model="ir.ui.view">
        <field name="name">dtech.dispatch.order.tree</field>
        <field name="model">dtech.dispatch.order</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'"
                  decoration-warning="state == 'in_progress'"
                  decoration-success="state == 'done'"
                  decoration-muted="state == 'cancelled'"
                  class="o_list_view">
                <field name="name"/>
                <field name="date"/>
                <field name="user_id"/>
                <field name="priority" widget="priority"/>
                <field name="total_quantity" sum="Total"/>
                <field name="dispatched_quantity" sum="Dispatched"/>
                <field name="remaining_quantity" sum="Remaining" 
                       decoration-danger="remaining_quantity &gt; 0"/>
                <field name="sale_order_count"/>
                <field name="state" widget="badge" 
                       decoration-info="state == 'draft'"
                       decoration-warning="state == 'in_progress'"
                       decoration-success="state == 'done'"
                       decoration-muted="state == 'cancelled'"/>
            </tree>
        </field>
    </record>
    
    <!-- Enhanced Search View -->
    <record id="view_dispatch_order_search" model="ir.ui.view">
        <field name="name">dtech.dispatch.order.search</field>
        <field name="model">dtech.dispatch.order</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="purchase_order_ids"/>
                <field name="partner_ids"/>
                <field name="product_ids"/>
                <field name="user_id"/>
                
                <filter name="my_dispatches" string="My Dispatches" 
                        domain="[('user_id', '=', uid)]"
                        icon="fa-user"/>
                <filter name="draft" string="Draft" 
                        domain="[('state', '=', 'draft')]"
                        icon="fa-pencil"/>
                <filter name="in_progress" string="In Progress" 
                        domain="[('state', '=', 'in_progress')]"
                        icon="fa-play"/>
                <filter name="pending" string="Pending Items" 
                        domain="[('remaining_quantity', '>', 0)]"
                        icon="fa-clock-o"/>
                
                <separator/>
                <filter name="urgent" string="High Priority" 
                        domain="[('priority', 'in', ['1', '2'])]"
                        icon="fa-exclamation"/>
                <filter name="today" string="Today" 
                        domain="[('date', '=', context_today().strftime('%Y-%m-%d'))]"
                        icon="fa-calendar"/>
                
                <group expand="0" string="Group By">
                    <filter name="group_state" string="Status" context="{'group_by': 'state'}"/>
                    <filter name="group_user" string="Responsible" context="{'group_by': 'user_id'}"/>
                    <filter name="group_priority" string="Priority" context="{'group_by': 'priority'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>
    <!-- Dispatch Line Search View -->
    <record id="view_dtech_dispatch_line_search" model="ir.ui.view">
        <field name="name">dtech.dispatch.line.search</field>
        <field name="model">dtech.dispatch.line</field>
        <field name="arch" type="xml">
            <search string="Lignes de dispatch">
                <field name="product_id"/>
                <field name="partner_id"/>
                <separator/>
                <filter string="Non dispatché" name="not_dispatched" domain="[('dispatched_quantity', '=', 0)]"/>
                <filter string="Partiellement dispatché" name="partially_dispatched" domain="[('dispatched_quantity', '&gt;', 0), ('dispatched_quantity', '&lt;', 'quantity')]"/>
                <filter string="Totalement dispatché" name="fully_dispatched" domain="[('dispatched_quantity', '=', 'quantity')]"/>
                <group expand="0" string="Regrouper par">
                    <filter string="Client" name="partner" context="{'group_by': 'partner_id'}"/>
                    <filter string="Produit" name="product" context="{'group_by': 'product_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Dispatch Line Tree View -->
    <record id="view_dtech_dispatch_line_tree" model="ir.ui.view">
        <field name="name">dtech.dispatch.line.tree</field>
        <field name="model">dtech.dispatch.line</field>
        <field name="arch" type="xml">
            <tree string="Lignes de dispatch" editable="bottom" decoration-success="dispatched_quantity == quantity" decoration-warning="dispatched_quantity &gt; 0 and dispatched_quantity &lt; quantity" >
                <field name="dispatch_id"/>
                <field name="product_id"/>
                <field name="partner_id"/>
                <field name="quantity"/>
                <field name="available_quantity"/>
                <field name="dispatched_quantity"/>
            </tree>
        </field>
    </record>

    <!-- Dispatch Line Form View -->
    <record id="view_dtech_dispatch_line_form" model="ir.ui.view">
        <field name="name">dtech.dispatch.line.form</field>
        <field name="model">dtech.dispatch.line</field>
        <field name="arch" type="xml">
            <form string="Ligne de dispatch">
                <sheet>
                    <group>
                        <group>
                            <field name="dispatch_id"/>
                            <field name="product_id"/>
                            <field name="partner_id"/>
                            <field name="sale_order_id"/>
                        </group>
                        <group>
                            <field name="quantity"/>
                            <field name="available_quantity"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action pour le dispatch -->
    <record id="action_dtech_dispatch" model="ir.actions.act_window">
        <field name="name">Dispatch</field>
        <field name="res_model">dtech.dispatch.order</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Créer un nouveau dispatch
            </p>
            <p>
                Créez un nouveau dispatch pour gérer la répartition des bons de commande.
            </p>
        </field>
    </record>

    <!-- Dispatch Line Action -->
    <record id="action_dtech_dispatch_line" model="ir.actions.act_window">
        <field name="name">Lignes de dispatch</field>
        <field name="res_model">dtech.dispatch.line</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Aucune ligne de dispatch trouvée
            </p>
        </field>
    </record>

    

    <record id="view_dtech_dispatch_kanban" model="ir.ui.view">
        <field name="name">dtech.dispatch.kanban</field>
        <field name="model">dtech.dispatch.order</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column" sample="1">
                <field name="name"/>
                <field name="state"/>
                <field name="total_quantity"/>
                <field name="dispatched_quantity"/>
                <field name="remaining_quantity"/>
                <field name="user_id"/>
                <field name="date"/>
                <field name="priority"/>
                <field name="purchase_order_ids"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click" 
                             style="min-height: 120px; 
                                    border-radius: 6px; 
                                    border: 1px solid #e1e5e9; 
                                    box-shadow: 0 1px 3px rgba(0,0,0,0.05); 
                                    transition: all 0.2s ease; 
                                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);"
                             onmouseover="this.style.boxShadow='0 3px 8px rgba(0,0,0,0.08)'; this.style.transform='translateY(-1px)';"
                             onmouseout="this.style.boxShadow='0 1px 3px rgba(0,0,0,0.05)'; this.style.transform='translateY(0)';">
                            
                            <!-- Horizontal Layout Structure -->
                            <div style="display: flex; flex-direction: column; height: 100%;">
                                
                                <!-- Top Section: Header with Title, Date, Priority and State -->
                                <div style="display: flex; justify-content: space-between; align-items: center; 
                                            padding: 8px 12px; 
                                            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); 
                                            border-radius: 6px 6px 0 0;">
                                    
                                    <!-- Left: Title and Date -->
                                    <div style="display: flex; align-items: center; flex: 1; min-width: 0;">
                                        <strong style="color: white; 
                                                      font-size: 14px; 
                                                      font-weight: 600; 
                                                      margin-right: 12px; 
                                                      white-space: nowrap; 
                                                      overflow: hidden; 
                                                      text-overflow: ellipsis; 
                                                      max-width: 150px;">
                                            <field name="name"/>
                                        </strong>
                                        <div style="color: rgba(255,255,255,0.9); 
                                                   font-size: 11px; 
                                                   display: flex; 
                                                   align-items: center; 
                                                   white-space: nowrap;">
                                            <i class="fa fa-calendar" style="margin-right: 4px; opacity: 0.8;"/>
                                            <field name="date" widget="date"/>
                                        </div>
                                    </div>
                                    
                                    <!-- Right: Priority and State -->
                                    <div style="display: flex; align-items: center; gap: 6px;">
                                        <t t-if="record.priority.raw_value == '2'">
                                            <div style="background: #dc3545; 
                                                       color: white; 
                                                       font-size: 9px; 
                                                       padding: 2px 6px; 
                                                       border-radius: 10px; 
                                                       white-space: nowrap;">
                                                <i class="fa fa-exclamation-triangle" style="margin-right: 3px;"/>Urgent
                                            </div>
                                        </t>
                                        <t t-elif="record.priority.raw_value == '1'">
                                            <div style="background: #ffc107; 
                                                       color: #212529; 
                                                       font-size: 9px; 
                                                       padding: 2px 6px; 
                                                       border-radius: 10px; 
                                                       white-space: nowrap;">
                                                <i class="fa fa-exclamation" style="margin-right: 3px;"/>Urgent
                                            </div>
                                        </t>
                                        <div style="background: rgba(255,255,255,0.2); 
                                                   color: white; 
                                                   font-size: 10px; 
                                                   padding: 2px 8px; 
                                                   border-radius: 10px; 
                                                   white-space: nowrap;">
                                            <field name="state"/>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Main Content Area -->
                                <div style="display: flex; flex: 1; padding: 8px 12px;">
                                    
                                    <!-- Left Column: Quantities -->
                                    <div style="display: flex; flex-direction: column; justify-content: center; width: 40%; padding-right: 10px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                            <span style="color: #6c757d; font-size: 10px; width: 40px;">Total:</span>
                                            <div style="background: #f1f3f5; 
                                                       padding: 2px 6px; 
                                                       border-radius: 4px; 
                                                       font-size: 11px; 
                                                       font-weight: 600; 
                                                       color: #495057;
                                                       border-left: 2px solid #6c757d;">
                                                <field name="total_quantity"/>
                                            </div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                                            <span style="color: #6c757d; font-size: 10px; width: 40px;">Expédié:</span>
                                            <div style="background: #d4edda; 
                                                       padding: 2px 6px; 
                                                       border-radius: 4px; 
                                                       font-size: 11px; 
                                                       font-weight: 600; 
                                                       color: #155724;
                                                       border-left: 2px solid #28a745;">
                                                <field name="dispatched_quantity"/>
                                            </div>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="color: #6c757d; font-size: 10px; width: 40px;">Restant:</span>
                                            <div style="background: #fff3cd; 
                                                       padding: 2px 6px; 
                                                       border-radius: 4px; 
                                                       font-size: 11px; 
                                                       font-weight: 600; 
                                                       color: #856404;
                                                       border-left: 2px solid #ffc107;">
                                                <field name="remaining_quantity"/>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Right Column: Orders and User -->
                                    <div style="display: flex; flex-direction: column; justify-content: space-between; width: 60%; padding-left: 10px; border-left: 1px solid #e9ecef;">
                                        <!-- Orders -->
                                        <div>
                                            <div style="color: #6c757d; font-size: 10px; margin-bottom: 3px;">Commandes:</div>
                                            <div style="max-height: 30px; overflow: hidden;">
                                                <field name="purchase_order_ids" 
                                                       widget="many2many_tags" 
                                                       options="{'no_create_edit': True}"/>
                                            </div>
                                        </div>
                                        
                                        <!-- User and Actions -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 4px;">
                                            <!-- User -->
                                            <div style="display: flex; align-items: center; font-size: 10px; color: #6c757d;">
                                                <i class="fa fa-user" style="margin-right: 4px;"/>
                                                <span style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 100px;">
                                                    <field name="user_id"/>
                                                </span>
                                            </div>
                                            
                                            <!-- Action Buttons -->
                                            <div style="display: flex; gap: 4px;">
                                                <button name="action_view_sale_orders" 
                                                        type="object" 
                                                        style="padding: 3px 6px; 
                                                               font-size: 10px; 
                                                               border: 1px solid #dee2e6; 
                                                               background: white; 
                                                               border-radius: 4px;"
                                                        title="Voir les devis">
                                                    <i class="fa fa-file-text-o" style="color: #495057;"/>
                                                </button>
                                                <button name="action_view_pickings" 
                                                        type="object" 
                                                        style="padding: 3px 6px; 
                                                               font-size: 10px; 
                                                               border: 1px solid #dee2e6; 
                                                               background: white; 
                                                               border-radius: 4px;"
                                                        title="Voir bons de livraison">
                                                    <i class="fa fa-truck" style="color: #495057;"/>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Progress Bar at Bottom -->
                                <div style="padding: 0 12px 8px 12px;">
                                    <t t-if="record.total_quantity.raw_value and record.total_quantity.raw_value > 0">
                                        <t t-set="progress_percent" t-value="record.dispatched_quantity.raw_value / record.total_quantity.raw_value * 100"/>
                                        
                                        <div style="display: flex; align-items: center;">
                                            <div style="flex: 1; margin-right: 8px;">
                                                <div style="background: #e9ecef; 
                                                           height: 6px; 
                                                           border-radius: 3px; 
                                                           overflow: hidden;">
                                                    <div style="background: linear-gradient(90deg, #28a745 0%, #20c997 100%); 
                                                               height: 100%; 
                                                               transition: width 0.6s ease;"
                                                         t-attf-style="width: #{progress_percent}%">
                                                    </div>
                                                </div>
                                            </div>
                                            <small style="color: #6c757d; font-size: 10px; white-space: nowrap;">
                                                <t t-esc="Math.round(progress_percent)"/>%
                                            </small>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    
</odoo> 
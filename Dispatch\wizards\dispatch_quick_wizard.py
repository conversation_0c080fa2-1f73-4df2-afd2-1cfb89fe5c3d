import logging
from odoo import models, fields, api  # type: ignore
from odoo.exceptions import UserError, ValidationError  # type: ignore


class DispatchQuickWizard(models.TransientModel):
    _name = 'dispatch.quick.wizard'
    _description = 'Assistant de dispatch rapide'

    dispatch_id = fields.Many2one('dtech.dispatch.order', string='Dispatch', required=True)
    available_product_ids = fields.Many2many('product.product', compute='_compute_available_options', store=False)
    available_partner_ids = fields.Many2many('res.partner', compute='_compute_available_options', store=False)
    product_id_filter = fields.Many2one('product.product', string='Filtrer par produit', domain="[('id', 'in', available_product_ids)]")
    partner_id_filter = fields.Many2one('res.partner', string='Filtrer par client', domain="[('id', 'in', available_partner_ids)]")
    
    # Changed to One2many for proper wizard line management
    wizard_line_ids = fields.One2many('dispatch.quick.wizard.line', 'wizard_id', string='Lignes de dispatch')
    
    # Use the same field but with domain for filtering in the view
    filtered_line_ids = fields.One2many('dispatch.quick.wizard.line', 'wizard_id', 
                                       string='Lignes filtrées')

    @api.depends('dispatch_id')
    def _compute_available_options(self):
        """Compute available products and partners for filtering"""
        for wizard in self:
            if wizard.dispatch_id:
                # Get products from purchase orders
                purchase_lines = wizard.dispatch_id.purchase_order_ids.mapped('order_line')
                products = purchase_lines.mapped('product_id')
                
                # Get partners from dispatch lines
                partners = wizard.dispatch_id.dispatch_line_ids.mapped('partner_id')
                
                logging.warning(f"Available products: {products}, Available partners: {partners}")
                wizard.available_product_ids = products
                wizard.available_partner_ids = partners
            else:
                wizard.available_product_ids = self.env['product.product']
                wizard.available_partner_ids = self.env['res.partner']

    @api.onchange('product_id_filter', 'partner_id_filter')
    def _onchange_filters(self):
        """Update visibility of wizard lines based on filters"""
        # This will be handled by the domain in the view
        pass

    @api.model
    def default_get(self, fields_list):
        """Initialize wizard with dispatch lines data"""
        res = super().default_get(fields_list)
        
        # Get dispatch from context
        dispatch_id = self._context.get('active_id')
        if dispatch_id:
            dispatch = self.env['dtech.dispatch.order'].browse(dispatch_id)
            res['dispatch_id'] = dispatch_id
            

            wizard_lines = []
            for line in dispatch.dispatch_line_ids:
                wizard_lines.append((0, 0, {
                    'dispatch_line_id': line.id,
                    'product_id': line.product_id.id,
                    'partner_id': line.partner_id.id,
                    'sale_order_id': line.sale_order_id.id,
                    'quantity': line.quantity,
                    'dispatched_quantity': line.dispatched_quantity,
                    'available_quantity': line.available_quantity,
                    'suggested_quantity': line.suggested_quantity,
                }))
            
            res['wizard_line_ids'] = wizard_lines
            
        return res

    def action_apply(self):
        """Apply wizard changes to dispatch lines"""
        self.ensure_one()
        
        if not self.wizard_line_ids:
            raise UserError("Aucune ligne à traiter.")
        
        # Validate quantities
        for wizard_line in self.wizard_line_ids:
            if wizard_line.dispatched_quantity < 0:
                raise ValidationError(
                    f"La quantité dispatchée ne peut pas être négative pour le produit "
                    f"{wizard_line.product_id.name}."
                )
        
        # Group by product to check total dispatched quantities
        product_totals = {}
        for wizard_line in self.wizard_line_ids:
            product_id = wizard_line.product_id.id
            if product_id not in product_totals:
                product_totals[product_id] = {
                    'total_dispatched': 0,
                    'available': wizard_line.available_quantity,
                    'product_name': wizard_line.product_id.name
                }
            product_totals[product_id]['total_dispatched'] += wizard_line.dispatched_quantity
        
        # Validate total quantities per product
        for product_id, data in product_totals.items():
            if data['total_dispatched'] > data['available']:
                raise ValidationError(
                    f"Le total des quantités dispatchées pour le produit "
                    f"{data['product_name']} ({data['total_dispatched']}) "
                    f"dépasse la quantité disponible ({data['available']})."
                )
        
        logging.warning(f"Updating dispatch lines for dispatch: {self.dispatch_id.name}")
        
        # Update dispatch lines
        for wizard_line in self.wizard_line_ids:
            if wizard_line.dispatch_line_id:
                wizard_line.dispatch_line_id.write({
                    'dispatched_quantity': wizard_line.dispatched_quantity,
                    'partner_id': wizard_line.partner_id.id,
                    'sale_order_id': wizard_line.sale_order_id.id if wizard_line.sale_order_id else False,

                })
                logging.warning(
                    f"Updated line {wizard_line.dispatch_line_id.id}: "
                    f"{wizard_line.product_id.name} -> {wizard_line.dispatched_quantity}"
                )
        
        return {
            'type': 'ir.actions.act_window_close',
        }

    def action_reset_quantities(self):
        """Reset all dispatched quantities to 0"""
        for line in self.wizard_line_ids:
            line.dispatched_quantity = 0
        return {'type': 'ir.actions.do_nothing'}

    def action_apply_suggestions(self):
        """Apply suggested quantities to all lines"""
        for line in self.wizard_line_ids:
            line.dispatched_quantity = line.suggested_quantity
        return {'type': 'ir.actions.do_nothing'}


